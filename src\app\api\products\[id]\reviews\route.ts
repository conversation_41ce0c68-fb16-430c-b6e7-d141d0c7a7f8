import { NextResponse } from 'next/server';
import dbConnect from '@/lib/mongodb';
import Review from '../../../../../../models/Review';

// GET /api/products/[id]/reviews - Get all reviews for a specific product
export async function GET(req: Request, { params }: { params: { id: string } }) {
  try {
    await dbConnect();

    const { searchParams } = new URL(req.url);
    const limit = parseInt(searchParams.get('limit') || '10');
    const page = parseInt(searchParams.get('page') || '1');
    const sortBy = searchParams.get('sortBy') || 'newest'; // newest, oldest, highest, lowest, helpful

    const productId = params.id;
    const skip = (page - 1) * limit;

    // Build sort criteria
    let sortCriteria: any = { createdAt: -1 }; // default: newest first
    switch (sortBy) {
      case 'oldest':
        sortCriteria = { createdAt: 1 };
        break;
      case 'highest':
        sortCriteria = { rating: -1, createdAt: -1 };
        break;
      case 'lowest':
        sortCriteria = { rating: 1, createdAt: -1 };
        break;
      case 'helpful':
        sortCriteria = { helpfulVotes: -1, createdAt: -1 };
        break;
    }

    // Get reviews for the product
    const reviews = await Review.find({ productId })
      .populate('userId', 'name')
      .sort(sortCriteria)
      .skip(skip)
      .limit(limit);

    // Get total count
    const total = await Review.countDocuments({ productId });

    // Calculate review statistics
    const stats = await Review.aggregate([
      { $match: { productId } },
      {
        $group: {
          _id: null,
          averageRating: { $avg: '$rating' },
          totalReviews: { $sum: 1 },
          ratingDistribution: {
            $push: '$rating'
          }
        }
      }
    ]);

    // Calculate rating distribution
    let ratingDistribution = { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 };
    if (stats.length > 0) {
      stats[0].ratingDistribution.forEach((rating: number) => {
        ratingDistribution[rating as keyof typeof ratingDistribution]++;
      });
    }

    const reviewStats = stats.length > 0 ? {
      averageRating: Math.round(stats[0].averageRating * 10) / 10,
      totalReviews: stats[0].totalReviews,
      ratingDistribution
    } : {
      averageRating: 0,
      totalReviews: 0,
      ratingDistribution
    };

    return NextResponse.json({
      reviews,
      stats: reviewStats,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching product reviews:', error);
    return NextResponse.json({ message: 'Internal server error' }, { status: 500 });
  }
}

// POST /api/products/[id]/reviews - Create a review for a specific product
export async function POST(req: Request, { params }: { params: { id: string } }) {
  try {
    const { getServerSession } = await import('next-auth/next');
    const { authOptions } = await import('@/app/api/auth/[...nextauth]/route');

    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    await dbConnect();

    const { rating, comment, title } = await req.json();
    const productId = params.id;

    if (!rating || !comment) {
      return NextResponse.json({
        message: 'Missing required fields: rating, comment'
      }, { status: 400 });
    }

    if (rating < 1 || rating > 5) {
      return NextResponse.json({
        message: 'Rating must be between 1 and 5'
      }, { status: 400 });
    }

    // Check if user already reviewed this product
    const existingReview = await Review.findOne({
      userId: session.user.id,
      productId: productId
    });

    if (existingReview) {
      return NextResponse.json({
        message: 'You have already reviewed this product'
      }, { status: 409 });
    }

    const review = new Review({
      userId: session.user.id,
      productId,
      rating,
      comment,
      title: title || '',
      verifiedPurchase: false // TODO: Check if user actually purchased this product
    });

    await review.save();
    await review.populate('userId', 'name');

    return NextResponse.json({
      message: 'Review created successfully',
      review
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating product review:', error);
    return NextResponse.json({ message: 'Internal server error' }, { status: 500 });
  }
}
