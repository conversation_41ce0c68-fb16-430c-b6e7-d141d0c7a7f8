# Image Setup Guide for CottonPrint

## 📁 Directory Structure

Create the following folder structure in your project:

```
cottonprint/
├── public/
│   ├── images/
│   │   ├── products/
│   │   │   ├── basic-tshirt.png
│   │   │   ├── v-neck.png
│   │   │   └── hoodie.png
│   │   ├── logo/
│   │   │   └── cottonprint-logo.png
│   │   └── placeholders/
│   │       └── product-placeholder.png
│   └── favicon.ico
```

## 🖼️ How to Add Images

### Step 1: Create the directories
```bash
mkdir -p public/images/products
mkdir -p public/images/logo
mkdir -p public/images/placeholders
```

### Step 2: Add your product images
Place your product images in `public/images/products/`:
- `basic-tshirt.png` (or .jpg)
- `v-neck.png` (or .jpg)
- `hoodie.png` (or .jpg)

### Step 3: Reference images in your code
In Next.js, images in the `public` folder are served from the root URL. 

**✅ Correct way:**
```javascript
// For images in public/images/products/
image: '/images/products/basic-tshirt.png'

// For images in public/images/logo/
image: '/images/logo/cottonprint-logo.png'
```

**❌ Wrong way:**
```javascript
// Don't include 'public' in the path
image: '/public/images/products/basic-tshirt.png' // ❌ Wrong
```

## 🎨 Using Next.js Image Component (Recommended)

For better performance, use Next.js `Image` component:

```jsx
import Image from 'next/image';

// In your component
<Image
  src="/images/products/basic-tshirt.png"
  alt="Basic T-Shirt"
  width={400}
  height={400}
  className="w-full h-full object-cover"
/>
```

## 📱 Responsive Images

For different screen sizes, you can use responsive images:

```jsx
<Image
  src="/images/products/basic-tshirt.png"
  alt="Basic T-Shirt"
  fill
  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
  className="object-cover"
/>
```

## 🔄 Placeholder Images

Create a placeholder image for products without images:

```jsx
const getProductImage = (product) => {
  return product.image || '/images/placeholders/product-placeholder.png';
};

// Usage
<img 
  src={getProductImage(product)} 
  alt={product.name}
  className="w-full h-64 object-cover"
/>
```

## 📋 Image Requirements

### Recommended specifications:
- **Format**: PNG or JPG
- **Size**: 800x800px for product images (square aspect ratio)
- **File size**: Under 500KB for web optimization
- **Quality**: 80-90% compression for JPG

### Naming conventions:
- Use lowercase letters
- Use hyphens instead of spaces
- Be descriptive: `basic-tshirt.png` not `img1.png`

## 🛠️ Updated Code Examples

Your product data should now look like this:

```javascript
const products = [
  {
    id: '1',
    name: 'Basic T-Shirt',
    price: 15.99,
    image: '/images/products/basic-tshirt.png', // ✅ Correct path
    description: 'A comfortable and versatile basic t-shirt.',
  },
  // ... more products
];
```

## 🚀 Quick Setup Commands

If you want to add sample images quickly:

```bash
# Navigate to your project directory
cd cottonprint

# Create the directory structure
mkdir -p public/images/products
mkdir -p public/images/logo
mkdir -p public/images/placeholders

# You can now add your images to these folders
```

## 📝 Notes

1. **Static Assets**: Images in `public/` are served statically
2. **Build Time**: Images are available immediately after adding them
3. **SEO**: Always include meaningful `alt` attributes
4. **Performance**: Consider using Next.js `Image` component for automatic optimization
5. **Caching**: Images in `public/` are cached by browsers

## 🔍 Troubleshooting

**Image not showing?**
- Check the file path (no `/public/` prefix)
- Verify the file exists in the correct directory
- Check file permissions
- Ensure the file extension matches the path

**Image too large?**
- Compress images before adding them
- Use appropriate dimensions (800x800px recommended)
- Consider using WebP format for better compression
