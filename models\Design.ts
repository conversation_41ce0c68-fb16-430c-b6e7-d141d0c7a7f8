import mongoose from 'mongoose';

const DesignSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  designName: {
    type: String,
    required: true,
  },
  imageUrl: {
    type: String,
    required: true,
  },
  textElements: [
    {
      content: String,
      color: String,
      fontSize: Number,
      x: Number,
      y: Number,
    },
  ],
  imageElements: [
    {
      imageUrl: String,
      x: Number,
      y: Number,
      width: Number,
      height: Number,
    },
  ],
  createdAt: {
    type: Date,
    default: Date.now,
  },
});

export default mongoose.models.Design || mongoose.model('Design', DesignSchema);