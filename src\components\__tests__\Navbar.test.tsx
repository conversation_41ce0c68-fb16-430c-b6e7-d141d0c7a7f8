// Basic test file for Navbar component
// Note: This requires additional testing dependencies to be installed

import { render, screen } from '@testing-library/react';
import { SessionProvider } from 'next-auth/react';
import { CartProvider } from '@/context/CartContext';
import Navbar from '../Navbar';

// Mock next/navigation
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
  }),
}));

const MockProviders = ({ children, session = null }: { children: React.ReactNode; session?: any }) => (
  <SessionProvider session={session}>
    <CartProvider>
      {children}
    </CartProvider>
  </SessionProvider>
);

describe('Navbar', () => {
  it('renders the CottonPrint brand', () => {
    render(
      <MockProviders>
        <Navbar />
      </MockProviders>
    );
    
    expect(screen.getByText('CottonPrint')).toBeInTheDocument();
  });

  it('shows navigation links', () => {
    render(
      <MockProviders>
        <Navbar />
      </MockProviders>
    );
    
    expect(screen.getByText('Home')).toBeInTheDocument();
    expect(screen.getByText('Products')).toBeInTheDocument();
    expect(screen.getByText('Design')).toBeInTheDocument();
  });

  it('shows sign in/up buttons when not authenticated', () => {
    render(
      <MockProviders>
        <Navbar />
      </MockProviders>
    );
    
    expect(screen.getByText('Sign In')).toBeInTheDocument();
    expect(screen.getByText('Sign Up')).toBeInTheDocument();
  });

  it('shows user menu when authenticated', () => {
    const mockSession = {
      user: {
        id: '1',
        name: 'John Doe',
        email: '<EMAIL>',
      },
    };

    render(
      <MockProviders session={mockSession}>
        <Navbar />
      </MockProviders>
    );
    
    expect(screen.getByText('John Doe')).toBeInTheDocument();
  });
});
