import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { NextResponse } from 'next/server';
import dbConnect from '@/lib/mongodb';
import Review from '../../../../models/Review';

// GET /api/reviews - Get all reviews (with optional filtering)
export async function GET(req: Request) {
  try {
    await dbConnect();

    const { searchParams } = new URL(req.url);
    const productId = searchParams.get('productId');
    const userId = searchParams.get('userId');
    const limit = parseInt(searchParams.get('limit') || '10');
    const page = parseInt(searchParams.get('page') || '1');

    let query: any = {};
    if (productId) query.productId = productId;
    if (userId) query.userId = userId;

    const skip = (page - 1) * limit;

    const reviews = await Review.find(query)
      .populate('userId', 'name')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    const total = await Review.countDocuments(query);

    return NextResponse.json({
      reviews,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching reviews:', error);
    return NextResponse.json({ message: 'Internal server error' }, { status: 500 });
  }
}

// POST /api/reviews - Create a new review
export async function POST(req: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    await dbConnect();

    const { productId, rating, comment, title } = await req.json();

    if (!productId || !rating || !comment) {
      return NextResponse.json({ 
        message: 'Missing required fields: productId, rating, comment' 
      }, { status: 400 });
    }

    if (rating < 1 || rating > 5) {
      return NextResponse.json({ 
        message: 'Rating must be between 1 and 5' 
      }, { status: 400 });
    }

    // Check if user already reviewed this product
    const existingReview = await Review.findOne({
      userId: session.user.id,
      productId: productId
    });

    if (existingReview) {
      return NextResponse.json({ 
        message: 'You have already reviewed this product' 
      }, { status: 409 });
    }

    const review = new Review({
      userId: session.user.id,
      productId,
      rating,
      comment,
      title: title || '',
      verifiedPurchase: false // TODO: Check if user actually purchased this product
    });

    await review.save();

    // Populate user info before returning
    await review.populate('userId', 'name');

    return NextResponse.json({ 
      message: 'Review created successfully', 
      review 
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating review:', error);
    return NextResponse.json({ message: 'Internal server error' }, { status: 500 });
  }
}
