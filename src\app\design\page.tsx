'use client';

import React, { useState, useRef } from 'react';

interface Position {
  x: number;
  y: number;
}

export default function DesignEditor() {
  const [text, setText] = useState('');
  const [textColor, setTextColor] = useState('#000000');
  const [fontSize, setFontSize] = useState(24);
  const [image, setImage] = useState<string | null>(null);
  const [textPosition, setTextPosition] = useState<Position>({ x: 50, y: 50 });
  const [error, setError] = useState<string>('');
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const imageRef = useRef<HTMLImageElement | null>(null);

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Validate file type
      if (!file.type.startsWith('image/')) {
        setError('Please select a valid image file');
        return;
      }

      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        setError('Image size must be less than 5MB');
        return;
      }

      setError('');
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result;
        if (typeof result === 'string') {
          setImage(result);
          // Preload the image
          const img = new Image();
          img.src = result;
          img.onload = () => {
            imageRef.current = img;
            drawCanvas();
          };
        }
      };
      reader.readAsDataURL(file);
    }
  };

  const drawCanvas = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Draw background image if available
    if (image && imageRef.current) {
      ctx.drawImage(imageRef.current, 0, 0, canvas.width, canvas.height);
    }

    // Draw text if available
    if (text.trim()) {
      ctx.fillStyle = textColor;
      ctx.font = `${fontSize}px Arial`;
      ctx.textBaseline = 'top';
      ctx.fillText(text, textPosition.x, textPosition.y);
    }
  };

  const handleFontSizeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(e.target.value);
    if (value >= 8 && value <= 100) {
      setFontSize(value);
      setError('');
    } else {
      setError('Font size must be between 8 and 100');
    }
  };

  const handleCanvasClick = (e: React.MouseEvent<HTMLCanvasElement>) => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    setTextPosition({ x, y });
  };

  const saveDesign = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const link = document.createElement('a');
    link.download = 'tshirt-design.png';
    link.href = canvas.toDataURL();
    link.click();
  };

  React.useEffect(() => {
    drawCanvas();
  }, [text, textColor, fontSize, textPosition, image]);

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">T-Shirt Design Editor</h1>

      {/* Error Message */}
      {error && (
        <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
          {error}
        </div>
      )}

      <div className="flex flex-col lg:flex-row gap-6">
        {/* Canvas Section */}
        <div className="lg:w-1/2">
          <div className="bg-gray-50 p-4 rounded-lg">
            <h2 className="text-lg font-medium mb-3">Design Preview</h2>
            <div className="flex justify-center">
              <canvas
                ref={canvasRef}
                width="400"
                height="400"
                className="border-2 border-gray-300 rounded cursor-crosshair max-w-full h-auto"
                onClick={handleCanvasClick}
                title="Click to position text"
              />
            </div>
            <p className="text-sm text-gray-600 mt-2 text-center">
              Click on the canvas to position your text
            </p>
          </div>
        </div>

        {/* Controls Section */}
        <div className="lg:w-1/2 space-y-4">
          <div className="bg-white p-4 rounded-lg border">
            <h2 className="text-lg font-medium mb-4">Design Controls</h2>

            {/* Text Input */}
            <div className="mb-4">
              <label htmlFor="textInput" className="block text-sm font-medium text-gray-700 mb-1">
                Add Text
              </label>
              <input
                type="text"
                id="textInput"
                placeholder="Enter your text here..."
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                value={text}
                onChange={(e) => setText(e.target.value)}
                maxLength={50}
              />
              <p className="text-xs text-gray-500 mt-1">{text.length}/50 characters</p>
            </div>

            {/* Text Color */}
            <div className="mb-4">
              <label htmlFor="textColor" className="block text-sm font-medium text-gray-700 mb-1">
                Text Color
              </label>
              <div className="flex items-center space-x-2">
                <input
                  type="color"
                  id="textColor"
                  className="w-12 h-10 border border-gray-300 rounded cursor-pointer"
                  value={textColor}
                  onChange={(e) => setTextColor(e.target.value)}
                />
                <input
                  type="text"
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  value={textColor}
                  onChange={(e) => setTextColor(e.target.value)}
                  placeholder="#000000"
                />
              </div>
            </div>

            {/* Font Size */}
            <div className="mb-4">
              <label htmlFor="fontSize" className="block text-sm font-medium text-gray-700 mb-1">
                Font Size: {fontSize}px
              </label>
              <input
                type="range"
                id="fontSize"
                min="8"
                max="100"
                className="w-full"
                value={fontSize}
                onChange={handleFontSizeChange}
              />
              <div className="flex justify-between text-xs text-gray-500 mt-1">
                <span>8px</span>
                <span>100px</span>
              </div>
            </div>

            {/* Image Upload */}
            <div className="mb-6">
              <label htmlFor="imageUpload" className="block text-sm font-medium text-gray-700 mb-1">
                Background Image
              </label>
              <input
                type="file"
                id="imageUpload"
                accept="image/*"
                className="w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100 file:cursor-pointer cursor-pointer"
                onChange={handleImageUpload}
              />
              <p className="text-xs text-gray-500 mt-1">Max size: 5MB. Supported: JPG, PNG, GIF</p>
            </div>

            {/* Action Buttons */}
            <div className="space-y-2">
              <button
                onClick={saveDesign}
                className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
              >
                Save Design
              </button>
              <button
                onClick={() => {
                  setText('');
                  setImage(null);
                  setTextPosition({ x: 50, y: 50 });
                  setError('');
                  imageRef.current = null;
                }}
                className="w-full bg-gray-200 text-gray-800 py-2 px-4 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors"
              >
                Clear All
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}