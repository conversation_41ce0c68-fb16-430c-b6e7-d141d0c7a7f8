'use client';

import React from 'react';
import { useParams } from 'next/navigation';
import Link from 'next/link';
import ReviewsList from '@/components/ReviewsList';

// Mock product data - in a real app, this would come from an API
const products = [
  {
    id: '1',
    name: 'Basic T-Shirt',
    price: 15.99,
    image: '/images/basic-tshirt.png',
  },
  {
    id: '2',
    name: 'Premium V-Neck',
    price: 22.50,
    image: '/images/v-neck.png',
  },
  {
    id: '3',
    name: 'Hood<PERSON>',
    price: 39.99,
    image: '/images/hoodie.png',
  },
];

export default function ProductReviewsPage() {
  const params = useParams();
  const productId = params.id as string;
  
  const product = products.find(p => p.id === productId);

  if (!product) {
    return (
      <div className="container mx-auto p-4">
        <div className="text-center py-12">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Product Not Found</h1>
          <p className="text-gray-600 mb-6">The product you're looking for doesn't exist.</p>
          <Link 
            href="/products" 
            className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700"
          >
            Back to Products
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4">
      {/* Breadcrumb */}
      <nav className="mb-6">
        <ol className="flex items-center space-x-2 text-sm text-gray-500">
          <li><Link href="/" className="hover:text-gray-700">Home</Link></li>
          <li>/</li>
          <li><Link href="/products" className="hover:text-gray-700">Products</Link></li>
          <li>/</li>
          <li><Link href={`/products/${productId}`} className="hover:text-gray-700">{product.name}</Link></li>
          <li>/</li>
          <li className="text-gray-900">Reviews</li>
        </ol>
      </nav>

      {/* Product Header */}
      <div className="bg-white border border-gray-200 rounded-lg p-6 mb-8">
        <div className="flex items-center space-x-4">
          <div className="w-16 h-16 bg-gray-100 rounded-lg overflow-hidden flex-shrink-0">
            <img 
              src={product.image} 
              alt={product.name}
              className="w-full h-full object-cover"
            />
          </div>
          <div className="flex-1">
            <h1 className="text-2xl font-bold text-gray-900">{product.name} - Reviews</h1>
            <p className="text-lg text-gray-600">${product.price.toFixed(2)}</p>
          </div>
          <div className="flex space-x-3">
            <Link 
              href={`/products/${productId}`}
              className="bg-gray-100 text-gray-900 px-4 py-2 rounded-lg hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
            >
              Back to Product
            </Link>
          </div>
        </div>
      </div>

      {/* Reviews Section */}
      <div className="bg-white rounded-lg">
        <ReviewsList productId={productId} />
      </div>
    </div>
  );
}
