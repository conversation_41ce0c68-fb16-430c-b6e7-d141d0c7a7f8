'use client';

import React from 'react';
import { useParams } from 'next/navigation';
import Link from 'next/link';
import ReviewsList from '@/components/ReviewsList';

// Mock product data - in a real app, this would come from an API
const products = [
  {
    id: '1',
    name: 'Basic T-Shirt',
    price: 15.99,
    image: '/images/basic-tshirt.png',
    description: 'A comfortable and versatile basic t-shirt made from 100% cotton. Perfect for everyday wear.',
    features: [
      '100% Cotton',
      'Machine Washable',
      'Available in Multiple Colors',
      'Comfortable Fit'
    ]
  },
  {
    id: '2',
    name: 'Premium V-Neck',
    price: 22.50,
    image: '/images/v-neck.png',
    description: 'Soft and stylish v-neck t-shirt with a premium feel. Made from high-quality cotton blend.',
    features: [
      'Cotton Blend',
      'V-Neck Design',
      'Soft Touch',
      'Durable Construction'
    ]
  },
  {
    id: '3',
    name: 'Hoodie',
    price: 39.99,
    image: '/images/hoodie.png',
    description: 'Warm and cozy hoodie for all seasons. Features a comfortable hood and front pocket.',
    features: [
      'Fleece Lined',
      'Front Pocket',
      'Adjustable Hood',
      'Warm & Comfortable'
    ]
  },
];

export default function ProductDetailPage() {
  const params = useParams();
  const productId = params.id as string;
  
  const product = products.find(p => p.id === productId);

  if (!product) {
    return (
      <div className="container mx-auto p-4">
        <div className="text-center py-12">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Product Not Found</h1>
          <p className="text-gray-600 mb-6">The product you're looking for doesn't exist.</p>
          <Link 
            href="/products" 
            className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700"
          >
            Back to Products
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4">
      {/* Breadcrumb */}
      <nav className="mb-6">
        <ol className="flex items-center space-x-2 text-sm text-gray-500">
          <li><Link href="/" className="hover:text-gray-700">Home</Link></li>
          <li>/</li>
          <li><Link href="/products" className="hover:text-gray-700">Products</Link></li>
          <li>/</li>
          <li className="text-gray-900">{product.name}</li>
        </ol>
      </nav>

      {/* Product Details */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
        {/* Product Image */}
        <div className="aspect-square bg-gray-100 rounded-lg overflow-hidden">
          <img 
            src={product.image} 
            alt={product.name}
            className="w-full h-full object-cover"
          />
        </div>

        {/* Product Info */}
        <div className="space-y-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">{product.name}</h1>
            <p className="text-2xl font-semibold text-gray-900">${product.price.toFixed(2)}</p>
          </div>

          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">Description</h3>
            <p className="text-gray-700">{product.description}</p>
          </div>

          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">Features</h3>
            <ul className="list-disc list-inside space-y-1 text-gray-700">
              {product.features.map((feature, index) => (
                <li key={index}>{feature}</li>
              ))}
            </ul>
          </div>

          {/* Action Buttons */}
          <div className="space-y-3">
            <button className="w-full bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
              Add to Cart
            </button>
            <Link 
              href={`/products/${productId}/reviews`}
              className="block w-full text-center bg-gray-100 text-gray-900 py-3 px-6 rounded-lg hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
            >
              View All Reviews
            </Link>
          </div>
        </div>
      </div>

      {/* Reviews Section */}
      <div className="border-t pt-12">
        <h2 className="text-2xl font-bold text-gray-900 mb-8">Customer Reviews</h2>
        <ReviewsList productId={productId} />
      </div>
    </div>
  );
}
