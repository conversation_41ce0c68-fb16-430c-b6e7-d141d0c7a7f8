import React from 'react';
import Link from 'next/link';

const products = [
  {
    id: 1,
    name: 'Basic T-Shirt',
    price: 15.99,
    image: '/images/basic-tshirt.png',
    description: 'A comfortable and versatile basic t-shirt.',
  },
  {
    id: 2,
    name: 'Premium V-Neck',
    price: 22.50,
    image: '/images/v-neck.png',
    description: 'Soft and stylish v-neck t-shirt.',
  },
  {
    id: 3,
    name: '<PERSON><PERSON>',
    price: 39.99,
    image: '/images/hoodie.png',
    description: 'Warm and cozy hoodie for all seasons.',
  },
];

export default function ProductsPage() {
  return (
    <div className="container mx-auto p-4">
      <h1 className="text-3xl font-bold mb-6 text-center">Our T-Shirt Collection</h1>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {products.map((product) => (
          <div key={product.id} className="border rounded-lg shadow-lg overflow-hidden">
            <img src={product.image} alt={product.name} className="w-full h-64 object-cover" />
            <div className="p-4">
              <h2 className="text-xl font-semibold mb-2">{product.name}</h2>
              <p className="text-gray-700 mb-2">${product.price.toFixed(2)}</p>
              <p className="text-gray-600 text-sm mb-4">{product.description}</p>
              <Link href={`/products/${product.id}`} className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                View Details
              </Link>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}