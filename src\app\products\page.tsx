import React from 'react';
import Link from 'next/link';
import Image from 'next/image';

const products = [
  {
    id: 1,
    name: 'Basic T-Shirt',
    price: 15.99,
    image: '/images/products/basic-tshirt.png',
    description: 'A comfortable and versatile basic t-shirt.',
  },
  {
    id: 2,
    name: 'Premium V-Neck',
    price: 22.50,
    image: '/images/products/v-neck.png',
    description: 'Soft and stylish v-neck t-shirt.',
  },
  {
    id: 3,
    name: '<PERSON><PERSON>',
    price: 39.99,
    image: '/images/products/hoodie.png',
    description: 'Warm and cozy hoodie for all seasons.',
  },
];

export default function ProductsPage() {
  return (
    <div className="container mx-auto p-4">
      <h1 className="text-3xl font-bold mb-6 text-center">Our T-Shirt Collection</h1>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {products.map((product) => (
          <div key={product.id} className="border rounded-lg shadow-lg overflow-hidden bg-white hover:shadow-xl transition-shadow duration-300">
            <div className="relative h-64 w-full bg-gray-100">
              <Image
                src={product.image}
                alt={product.name}
                fill
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                className="object-cover"
                onError={(e) => {
                  // Fallback to a placeholder if image fails to load
                  e.currentTarget.src = '/images/placeholders/product-placeholder.png';
                }}
              />
            </div>
            <div className="p-4">
              <h2 className="text-xl font-semibold mb-2 text-gray-900">{product.name}</h2>
              <p className="text-blue-600 font-bold mb-2 text-lg">${product.price.toFixed(2)}</p>
              <p className="text-gray-600 text-sm mb-4 line-clamp-2">{product.description}</p>
              <Link
                href={`/products/${product.id}`}
                className="inline-block w-full text-center bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors duration-200 font-medium"
              >
                View Details
              </Link>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}