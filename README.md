# CottonPrint T-shirt design Application

![Build Status](https://img.shields.io/badge/build-passing-brightgreen)
![Version](https://img.shields.io/badge/version-1.0.0-blue)
![License](https://img.shields.io/badge/license-MIT-green)

## 1. Project Overview

CottonPrint is a modern tshirt design application built with Next.js, designed to provide a seamless shopping experience for custom cotton products. This platform allows users to browse a product catalog, design custom items, manage their shopping cart, and complete secure checkout flows. It features user authentication, product listings, a design studio, and order management.

## 2. Installation Instructions

To set up and run CottonPrint locally, follow these steps:

### Prerequisites

- Node.js (v18 or higher)
- npm or pnpm (recommended)
- MongoDB instance (local or cloud-hosted)

### Setup Steps

1.  **Clone the repository:**
    ```bash
    git clone https://github.com/maro14/cottonprint.git
    cd cottonprint
    ```

2.  **Install dependencies:**
    ```bash
    pnpm install
    # or npm install
    ```

3.  **Create a `.env.local` file:**
    Copy the `.env.example` file to `.env.local` and update the environment variables.
    ```bash
    cp .env.example .env.local
    ```

4.  **Update `.env.local` with your credentials:**
    ```
    MONGODB_URI=your_mongodb_connection_string
    NEXTAUTH_SECRET=your_nextauth_secret
    NEXTAUTH_URL=http://localhost:3000
    # Add any other necessary environment variables
    ```

5.  **Run the development server:**
    ```bash
    pnpm dev
    # or npm run dev
    ```

    The application will be accessible at `http://localhost:3000`.

## 3. Usage Guide

### User Authentication

-   **Sign Up:** Create a new account by navigating to `/auth/signup`.
-   **Sign In:** Log in to your existing account at `/auth/signin`.
-   **Sign Out:** Click the "Sign Out" button in the header when logged in.

### Browsing Products

-   Navigate to the "Products" section to view available items.
-   Click on a product to see its details.

### Designing Custom Products

-   Visit the "Design" section to use the custom design studio.
-   Follow the on-screen instructions to create your unique cotton product.

### Shopping Cart

-   Add products to your cart from product detail pages.
-   View and manage your cart contents in the "Cart" section.

### Checkout Process

-   Proceed to checkout from the cart page.
-   Enter shipping and payment information to complete your order.

### Order History

-   View your past orders in the "Orders" section (requires login).

## 4. Configuration Options

Environment variables are used for sensitive information and configuration. Create a `.env.local` file in the root directory and populate it with the following:

-   `MONGODB_URI`: Your MongoDB connection string. (e.g., `mongodb://localhost:27017/cottonprint` or a cloud URI).
-   `NEXTAUTH_SECRET`: A random string used to sign and encrypt JWTs. Generate a strong one (e.g., using `openssl rand -base64 32`).
-   `NEXTAUTH_URL`: The base URL of your application (e.g., `http://localhost:3000`).
-   `MIDTRANS_CLIENT_KEY`: Your Midtrans client key for payment gateway integration.
-   `MIDTRANS_SERVER_KEY`: Your Midtrans server key.

## 5. Contribution Guidelines

We welcome contributions to CottonPrint! To contribute, please follow these steps:

1.  **Fork the repository.**
2.  **Create a new branch** for your feature or bug fix: `git checkout -b feature/your-feature-name` or `git checkout -b bugfix/issue-description`.
3.  **Make your changes** and ensure they adhere to the project's coding standards.
4.  **Write clear, concise commit messages.**
5.  **Push your branch** to your forked repository.
6.  **Open a Pull Request** to the `main` branch of the original repository.

## 6. License Information

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.
