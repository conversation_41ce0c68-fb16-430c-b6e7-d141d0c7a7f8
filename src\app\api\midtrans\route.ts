import Midtrans from 'midtrans-client';
import { NextResponse } from 'next/server';

let snap = new Midtrans.Snap({
  isProduction: false,
  serverKey: process.env.MIDTRANS_SERVER_KEY,
  clientKey: process.env.MIDTRANS_CLIENT_KEY,
});

export async function POST(request) {
  const { cart, transaction_details, customer_details } = await request.json();

  const parameter = {
    transaction_details: {
      gross_amount: cart.reduce((total, item) => total + item.price * item.quantity, 0),
      order_id: transaction_details.order_id || `ORDER-${Math.floor(Math.random() * 1000000)}`,
    },
    item_details: cart.map(item => ({
      id: item.id,
      price: item.price,
      quantity: item.quantity,
      name: item.name
    })),
    customer_details: customer_details,
    credit_card: {
      secure: true,
    },
  };

  try {
    const transaction = await snap.createTransaction(parameter);
    return NextResponse.json({ token: transaction.token });
  } catch (error) {
    console.error('Error creating Midtrans transaction:', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}