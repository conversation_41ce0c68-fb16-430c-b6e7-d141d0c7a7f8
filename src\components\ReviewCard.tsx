'use client';

import React, { useState } from 'react';
import StarRating from './StarRating';

interface Review {
  _id: string;
  userId: {
    _id: string;
    name: string;
  };
  rating: number;
  comment: string;
  title?: string;
  helpfulVotes: number;
  verifiedPurchase: boolean;
  createdAt: string;
  updatedAt: string;
}

interface ReviewCardProps {
  review: Review;
  currentUserId?: string;
  onEdit?: (review: Review) => void;
  onDelete?: (reviewId: string) => void;
  onHelpful?: (reviewId: string) => void;
}

export default function ReviewCard({ 
  review, 
  currentUserId, 
  onEdit, 
  onDelete, 
  onHelpful 
}: ReviewCardProps) {
  const [showFullComment, setShowFullComment] = useState(false);
  const isOwner = currentUserId === review.userId._id;
  const commentPreview = review.comment.length > 200 
    ? review.comment.substring(0, 200) + '...' 
    : review.comment;

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm">
      {/* Header */}
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center">
            <span className="text-sm font-medium text-gray-700">
              {review.userId.name.charAt(0).toUpperCase()}
            </span>
          </div>
          <div>
            <h4 className="font-medium text-gray-900">{review.userId.name}</h4>
            <div className="flex items-center space-x-2">
              <StarRating rating={review.rating} size="sm" />
              {review.verifiedPurchase && (
                <span className="text-xs text-green-600 bg-green-50 px-2 py-1 rounded">
                  Verified Purchase
                </span>
              )}
            </div>
          </div>
        </div>
        
        {/* Actions for owner */}
        {isOwner && (
          <div className="flex space-x-2">
            {onEdit && (
              <button
                onClick={() => onEdit(review)}
                className="text-sm text-blue-600 hover:text-blue-800"
              >
                Edit
              </button>
            )}
            {onDelete && (
              <button
                onClick={() => onDelete(review._id)}
                className="text-sm text-red-600 hover:text-red-800"
              >
                Delete
              </button>
            )}
          </div>
        )}
      </div>

      {/* Review Title */}
      {review.title && (
        <h5 className="font-medium text-gray-900 mb-2">{review.title}</h5>
      )}

      {/* Review Comment */}
      <div className="mb-4">
        <p className="text-gray-700 leading-relaxed">
          {showFullComment ? review.comment : commentPreview}
        </p>
        {review.comment.length > 200 && (
          <button
            onClick={() => setShowFullComment(!showFullComment)}
            className="text-sm text-blue-600 hover:text-blue-800 mt-1"
          >
            {showFullComment ? 'Show less' : 'Read more'}
          </button>
        )}
      </div>

      {/* Footer */}
      <div className="flex items-center justify-between text-sm text-gray-500">
        <span>{formatDate(review.createdAt)}</span>
        
        <div className="flex items-center space-x-4">
          {/* Helpful button */}
          {onHelpful && !isOwner && (
            <button
              onClick={() => onHelpful(review._id)}
              className="flex items-center space-x-1 hover:text-gray-700"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L9 7v13m-3-4h-2m-2-2h2m0 0V9a2 2 0 012-2h2" />
              </svg>
              <span>Helpful ({review.helpfulVotes})</span>
            </button>
          )}
          
          {/* Show helpful count for owner */}
          {isOwner && review.helpfulVotes > 0 && (
            <span className="flex items-center space-x-1">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L9 7v13m-3-4h-2m-2-2h2m0 0V9a2 2 0 012-2h2" />
              </svg>
              <span>{review.helpfulVotes} helpful</span>
            </span>
          )}
        </div>
      </div>
    </div>
  );
}
