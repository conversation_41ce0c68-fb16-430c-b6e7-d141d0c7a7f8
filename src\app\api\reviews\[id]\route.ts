import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { NextResponse } from 'next/server';
import dbConnect from '@/lib/mongodb';
import Review from '../../../../../models/Review';
import mongoose from 'mongoose';

// GET /api/reviews/[id] - Get a specific review
export async function GET(req: Request, { params }: { params: { id: string } }) {
  try {
    await dbConnect();

    if (!mongoose.Types.ObjectId.isValid(params.id)) {
      return NextResponse.json({ message: 'Invalid review ID' }, { status: 400 });
    }

    const review = await Review.findById(params.id).populate('userId', 'name');

    if (!review) {
      return NextResponse.json({ message: 'Review not found' }, { status: 404 });
    }

    return NextResponse.json(review);
  } catch (error) {
    console.error('Error fetching review:', error);
    return NextResponse.json({ message: 'Internal server error' }, { status: 500 });
  }
}

// PUT /api/reviews/[id] - Update a review
export async function PUT(req: Request, { params }: { params: { id: string } }) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    await dbConnect();

    if (!mongoose.Types.ObjectId.isValid(params.id)) {
      return NextResponse.json({ message: 'Invalid review ID' }, { status: 400 });
    }

    const { rating, comment, title } = await req.json();

    if (!rating || !comment) {
      return NextResponse.json({ 
        message: 'Missing required fields: rating, comment' 
      }, { status: 400 });
    }

    if (rating < 1 || rating > 5) {
      return NextResponse.json({ 
        message: 'Rating must be between 1 and 5' 
      }, { status: 400 });
    }

    const review = await Review.findById(params.id);

    if (!review) {
      return NextResponse.json({ message: 'Review not found' }, { status: 404 });
    }

    // Check if the user owns this review
    if (review.userId.toString() !== session.user.id) {
      return NextResponse.json({ 
        message: 'You can only update your own reviews' 
      }, { status: 403 });
    }

    review.rating = rating;
    review.comment = comment;
    review.title = title || review.title;
    review.updatedAt = new Date();

    await review.save();
    await review.populate('userId', 'name');

    return NextResponse.json({ 
      message: 'Review updated successfully', 
      review 
    });
  } catch (error) {
    console.error('Error updating review:', error);
    return NextResponse.json({ message: 'Internal server error' }, { status: 500 });
  }
}

// DELETE /api/reviews/[id] - Delete a review
export async function DELETE(req: Request, { params }: { params: { id: string } }) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    await dbConnect();

    if (!mongoose.Types.ObjectId.isValid(params.id)) {
      return NextResponse.json({ message: 'Invalid review ID' }, { status: 400 });
    }

    const review = await Review.findById(params.id);

    if (!review) {
      return NextResponse.json({ message: 'Review not found' }, { status: 404 });
    }

    // Check if the user owns this review
    if (review.userId.toString() !== session.user.id) {
      return NextResponse.json({ 
        message: 'You can only delete your own reviews' 
      }, { status: 403 });
    }

    await Review.findByIdAndDelete(params.id);

    return NextResponse.json({ message: 'Review deleted successfully' });
  } catch (error) {
    console.error('Error deleting review:', error);
    return NextResponse.json({ message: 'Internal server error' }, { status: 500 });
  }
}
